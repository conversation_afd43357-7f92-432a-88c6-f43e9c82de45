from data.random_data_generator import random_data
from LVM.pls import PlsClass as mypls,plseval
from LVM.pca import PcaClass as pca,pcaeval
import numpy as np 

import pytest
# Run all tests in the `tests` folder
pytest.main(["tests/"])

from data.import_data import extract_data_from_sheet as exdata
import pandas as pd
df = pd.read_excel('data/data.xlsx', sheet_name='X',usecols='B:E',skiprows=1).to_numpy()
# df.set_index(df.columns[0],inplace=True)
print(df)



from src.LVM.pls import PlsClass as pls, plseval
import numpy as np
import unittest
from src.data.random_data_generator import random_data
import pytest
from src.benchmark.lvm_models.pls_sklear import plsSkleanr


N = 30
xvar = 6
yvar = 3
Ntes = 5
nCom = 4
num_clusters = 3

X, Y, X_test, Y_test = random_data(
    N=N, xvar=xvar, yvar=yvar, Ntest=Ntes, scaled=True)

benchmarkpls = plsSkleanr(X, Y, nCom)
yfit_benchmark, Ttesb, T2tesb, SPEtesb, = benchmarkpls.eval(X_test)

mypls = pls().fit(X, Y, n_component=nCom)
eval: plseval = mypls.evaluation(X_test)
yfit, Ttes, T2tes, SPEtes = eval.yfit, eval.tscore, eval.HT2, eval.spex


def test_xhat():
    mypls2 = pls().fit(X, Y, n_component=xvar)
    x_hat: plseval = mypls2.evaluation(X).xhat
    assert np.allclose(x_hat, X)

def test_num_com_setter():
    mypls1 = pls().fit(X[1:5, :], Y[1:5, :], n_component=6)
    assert mypls1.n_component == 3

    mypls2 = pls().fit(X, Y, n_component=xvar+1)
    assert mypls2.n_component == xvar

from LVM.pls import PlsClass as pls, plseval
from LVM.pca import PcaClass as pca, pcaeval
import numpy as np

from data.random_data_generator import random_data



N = 30
xvar = 6
yvar = 3
Ntes = 5
nCom = 4
num_clusters = 3

X, Y, X_test, Y_test = random_data(
    N=N, xvar=xvar, yvar=yvar, Ntest=Ntes, scaled=True)
pls_model=pls().fit(X,Y)
pca_model=pca().fit(X)
pls_model.visual_plot()


print(pls_model)
print(pca_model)

X=np.random.rand(30,8)
from src.LVM.spca import spca
__,R2=spca(X,plotting=False)
i=np.where(np.cumsum(R2)>.90)[0][0]
print(np.cumsum(R2))
print(i)

import numpy as np
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

np.random.seed(42)
X = np.random.rand(100, 3)

# Define number of clusters
n_clusters = 3

# Apply KMeans clustering
kmeans = KMeans(n_clusters=n_clusters, random_state=42)
kmeans.fit(X)

# Get the cluster labels and centroids
labels = kmeans.labels_

kmeans.predict(X)


labels


from LVM.spca import spca
sug_ord=np.array([4,1])
w=spca(X)
ww=spca(X,sugg_order=np.array([4]))
